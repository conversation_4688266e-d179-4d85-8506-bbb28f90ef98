console.log('Testing <NAME_EMAIL>...')

import http from 'http'

const postData = JSON.stringify({
  email: '<EMAIL>',
  password: 'Password123!'
})

console.log('Sending request to http://localhost:3001/api/auth/login')
console.log('Request body:', postData)

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/api/auth/login',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  }
}

const req = http.request(options, res => {
  console.log('\n=== RESPONSE ===')
  console.log('Status Code:', res.statusCode)
  console.log('Status Message:', res.statusMessage)
  console.log('Headers:', JSON.stringify(res.headers, null, 2))

  let data = ''
  res.on('data', chunk => {
    data += chunk
  })

  res.on('end', () => {
    console.log('\n=== RESPONSE BODY ===')
    try {
      const jsonData = JSON.parse(data)
      console.log(JSON.stringify(jsonData, null, 2))
    } catch (error) {
      console.log('Raw response (not JSON):', data)
    }
    console.log('\n=== END ===')
  })
})

req.on('error', error => {
  console.error('Request error:', error)
})

req.write(postData)
req.end()
