import { body, validationResult } from 'express-validator';

// Test what normalizeEmail does to our test email
const testEmail = '<EMAIL>';

console.log('Original email:', testEmail);

// Create a mock request object
const mockReq = {
  body: {
    email: testEmail
  }
};

const mockRes = {};
const mockNext = () => {};

// Create validation middleware
const emailValidator = body('email').isEmail().normalizeEmail();

// Run the validation
emailValidator(mockReq, mockRes, () => {
  console.log('Email after normalizeEmail:', mockReq.body.email);
  
  // Check validation result
  const errors = validationResult(mockReq);
  console.log('Validation errors:', errors.array());
  console.log('Is valid:', errors.isEmpty());
});
