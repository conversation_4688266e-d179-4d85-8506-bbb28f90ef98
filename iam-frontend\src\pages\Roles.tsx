import React, { useState, useEffect } from 'react'
import { useAuth } from '../hooks/useAuth'
import { apiService } from '../services/api'

import Modal from '../components/Modal'
import LoadingSpinner from '../components/LoadingSpinner'

const Roles: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([])
  const [groups, setGroups] = useState<Group[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isGroupModalOpen, setIsGroupModalOpen] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [formData, setFormData] = useState<CreateRoleRequest>({
    name: '',
    description: ''
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedGroupIds, setSelectedGroupIds] = useState<string[]>([])

  const { hasPermission } = useAuth()

  const canCreate = hasPermission('Roles', 'create')
  const canUpdate = hasPermission('Roles', 'update')
  const canDelete = hasPermission('Roles', 'delete')

  useEffect(() => {
    fetchRoles()
    fetchGroups()
  }, [])

  const fetchRoles = async () => {
    try {
      setLoading(true)
      const response = await apiService.getRoles()
      if (response.success && response.data) {
        setRoles(response.data)
      } else {
        setError(response.message || 'Failed to fetch roles')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to fetch roles'
      setError(errorMessage || 'Failed to fetch roles')
    } finally {
      setLoading(false)
    }
  }

  const fetchGroups = async () => {
    try {
      const response = await apiService.getGroups()
      if (response.success && response.data) {
        setGroups(response.data)
      }
    } catch (err: unknown) {
      console.error('Failed to fetch groups:', err)
    }
  }

  const handleOpenModal = (role?: Role) => {
    if (role) {
      setEditingRole(role)
      setFormData({
        name: role.name,
        description: role.description
      })
    } else {
      setEditingRole(null)
      setFormData({
        name: '',
        description: ''
      })
    }
    setFormErrors({})
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingRole(null)
    setFormData({
      name: '',
      description: ''
    })
    setFormErrors({})
  }

  const handleOpenGroupModal = (role: Role) => {
    setSelectedRole(role)
    // Pre-select groups that already have this role
    const roleGroupIds = role.groups?.map(group => group.id) || []
    setSelectedGroupIds(roleGroupIds)
    setIsGroupModalOpen(true)
  }

  const handleCloseGroupModal = () => {
    setIsGroupModalOpen(false)
    setSelectedRole(null)
    setSelectedGroupIds([])
  }

  const validateForm = (): boolean => {
    const errors: FormErrors = {}

    if (!formData.name.trim()) {
      errors.name = 'Role name is required'
    } else if (formData.name.length < 2) {
      errors.name = 'Role name must be at least 2 characters'
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      if (editingRole) {
        // Update role
        const response = await apiService.updateRole(editingRole.id, formData)
        if (response.success) {
          await fetchRoles()
          handleCloseModal()
        } else {
          setError(response.message || 'Failed to update role')
        }
      } else {
        // Create role
        const response = await apiService.createRole(formData)
        if (response.success) {
          await fetchRoles()
          handleCloseModal()
        } else {
          setError(response.message || 'Failed to create role')
        }
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to save role'
      setError(errorMessage || 'Failed to save role')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async (role: Role) => {
    if (!window.confirm(`Are you sure you want to delete role "${role.name}"?`)) {
      return
    }

    try {
      const response = await apiService.deleteRole(role.id)
      if (response.success) {
        await fetchRoles()
      } else {
        setError(response.message || 'Failed to delete role')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to delete role'
      setError(errorMessage || 'Failed to delete role')
    }
  }

  const handleGroupSelection = (groupId: string) => {
    setSelectedGroupIds(prev => {
      if (prev.includes(groupId)) {
        return prev.filter(id => id !== groupId)
      } else {
        return [...prev, groupId]
      }
    })
  }

  const handleAssignRolesToGroups = async () => {
    if (!selectedRole) return

    setIsSubmitting(true)
    try {
      // For each selected group, assign this role
      for (const groupId of selectedGroupIds) {
        await apiService.assignRolesToGroup(groupId, {
          roleIds: [selectedRole.id]
        })
      }

      // Remove role from groups that are no longer selected
      const currentGroupIds = selectedRole.groups?.map(g => g.id) || []
      const groupsToRemoveFrom = currentGroupIds.filter(id => !selectedGroupIds.includes(id))

      for (const groupId of groupsToRemoveFrom) {
        await apiService.removeRoleFromGroup(groupId, selectedRole.id)
      }

      await fetchRoles()
      handleCloseGroupModal()
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to assign roles to groups'
      setError(errorMessage || 'Failed to assign roles to groups')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleRemoveRoleFromGroup = async (groupId: string, roleId: string) => {
    if (!window.confirm('Are you sure you want to remove this role from the group?')) {
      return
    }

    try {
      const response = await apiService.removeRoleFromGroup(groupId, roleId)
      if (response.success) {
        await fetchRoles()
      } else {
        setError(response.message || 'Failed to remove role from group')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to remove role from group'
      setError(errorMessage || 'Failed to remove role from group')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Roles Management</h1>
        {canCreate && (
          <button onClick={() => handleOpenModal()} className="btn-primary">
            Add Role
          </button>
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-700">{error}</div>
          <button onClick={() => setError('')} className="mt-2 text-sm text-red-600 hover:text-red-800">
            Dismiss
          </button>
        </div>
      )}

      {/* Roles table */}
      <div className="card p-0">
        <div className="table-container">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">Name</th>
                <th className="table-header-cell">Description</th>
                <th className="table-header-cell">Groups</th>
                <th className="table-header-cell">Permissions</th>
                <th className="table-header-cell">Created</th>
                <th className="table-header-cell">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {roles.map(role => (
                <tr key={role.id}>
                  <td className="table-cell">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center mr-3">
                        <span className="text-white text-sm font-medium">{role.name[0].toUpperCase()}</span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{role.name}</div>
                      </div>
                    </div>
                  </td>
                  <td className="table-cell">
                    <div className="text-sm text-gray-900 max-w-xs truncate">{role.description}</div>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-900">{role.groups?.length || 0} groups</span>
                      {role.groups && role.groups.length > 0 && (
                        <div className="flex -space-x-1">
                          {role.groups.slice(0, 3).map(group => (
                            <div
                              key={group.id}
                              className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center border-2 border-white"
                              title={group.name}
                            >
                              <span className="text-white text-xs font-medium">{group.name[0].toUpperCase()}</span>
                            </div>
                          ))}
                          {role.groups.length > 3 && (
                            <div className="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center border-2 border-white">
                              <span className="text-white text-xs font-medium">+{role.groups.length - 3}</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="table-cell">
                    <span className="text-sm text-gray-900">{role.permissions?.length || 0} permissions</span>
                  </td>
                  <td className="table-cell">{new Date(role.createdAt).toLocaleDateString()}</td>
                  <td className="table-cell">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleOpenGroupModal(role)}
                        className="text-green-600 hover:text-green-900 text-sm"
                      >
                        Manage Groups
                      </button>
                      {canUpdate && (
                        <button
                          onClick={() => handleOpenModal(role)}
                          className="text-blue-600 hover:text-blue-900 text-sm"
                        >
                          Edit
                        </button>
                      )}
                      {canDelete && (
                        <button onClick={() => handleDelete(role)} className="text-red-600 hover:text-red-900 text-sm">
                          Delete
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {roles.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No roles found.</p>
            </div>
          )}
        </div>
      </div>

      {/* Role form modal */}
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={editingRole ? 'Edit Role' : 'Add Role'} size="md">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="form-label">
              Role Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              className={`form-input ${
                formErrors.name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              value={formData.name}
              onChange={handleInputChange}
              required
            />
            {formErrors.name && <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>}
          </div>

          <div>
            <label htmlFor="description" className="form-label">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              className={`form-input ${
                formErrors.description ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              value={formData.description}
              onChange={handleInputChange}
              required
            />
            {formErrors.description && <p className="mt-1 text-sm text-red-600">{formErrors.description}</p>}
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button type="button" onClick={handleCloseModal} className="btn-secondary">
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  {editingRole ? 'Updating...' : 'Creating...'}
                </>
              ) : editingRole ? (
                'Update Role'
              ) : (
                'Create Role'
              )}
            </button>
          </div>
        </form>
      </Modal>

      {/* Group assignment modal */}
      <Modal
        isOpen={isGroupModalOpen}
        onClose={handleCloseGroupModal}
        title={`Manage Groups - ${selectedRole?.name}`}
        size="lg"
      >
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Select groups to assign this role to. Groups that already have this role are pre-selected.
          </p>

          {/* Current role assignments */}
          {selectedRole?.groups && selectedRole.groups.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Current Assignments</h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {selectedRole.groups.map(group => (
                  <div key={group.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center mr-2">
                        <span className="text-white text-xs font-medium">{group.name[0].toUpperCase()}</span>
                      </div>
                      <span className="text-sm text-gray-900">{group.name}</span>
                    </div>
                    <button
                      onClick={() => handleRemoveRoleFromGroup(group.id, selectedRole.id)}
                      className="text-red-600 hover:text-red-800 text-xs"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Available groups */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Available Groups</h4>
            <div className="space-y-2 max-h-64 overflow-y-auto border border-gray-200 rounded p-2">
              {groups.map(group => (
                <label key={group.id} className="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedGroupIds.includes(group.id)}
                    onChange={() => handleGroupSelection(group.id)}
                    className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center mr-2">
                      <span className="text-white text-xs font-medium">{group.name[0].toUpperCase()}</span>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">{group.name}</div>
                      <div className="text-xs text-gray-500">{group.description}</div>
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button type="button" onClick={handleCloseGroupModal} className="btn-secondary">
              Cancel
            </button>
            <button
              onClick={handleAssignRolesToGroups}
              disabled={isSubmitting}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Updating...
                </>
              ) : (
                'Update Role Assignments'
              )}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default Roles
