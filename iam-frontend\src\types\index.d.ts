declare global {
  // User types
  interface User {
    id: string
    username: string
    email: string
    firstName: string
    lastName: string
    isActive: boolean
    createdAt: string
    updatedAt: string
    groups?: Group[]
  }

  interface CreateUserRequest {
    username: string
    email: string
    password: string
    firstName: string
    lastName: string
  }

  interface UpdateUserRequest {
    username?: string
    email?: string
    firstName?: string
    lastName?: string
    isActive?: boolean
  }

  // Group types
  interface Group {
    id: string
    name: string
    description: string
    createdAt: string
    updatedAt: string
    users?: User[]
    roles?: Role[]
  }

  interface CreateGroupRequest {
    name: string
    description: string
  }

  interface UpdateGroupRequest {
    name?: string
    description?: string
  }

  // Role types
  interface Role {
    id: string
    name: string
    description: string
    createdAt: string
    updatedAt: string
    groups?: Group[]
    permissions?: Permission[]
  }

  interface CreateRoleRequest {
    name: string
    description: string
  }

  interface UpdateRoleRequest {
    name?: string
    description?: string
  }

  // Module types
  interface Module {
    id: string
    name: string
    description: string
    createdAt: string
    updatedAt: string
    permissions?: Permission[]
  }

  interface CreateModuleRequest {
    name: string
    description: string
  }

  interface UpdateModuleRequest {
    name?: string
    description?: string
  }

  // Permission types
  interface Permission {
    id: string
    action: string
    moduleId: string
    module?: Module
    createdAt: string
    updatedAt: string
    roles?: Role[]
  }

  interface CreatePermissionRequest {
    action: string
    moduleId: string
  }

  interface UpdatePermissionRequest {
    action?: string
    moduleId?: string
  }

  // Authentication types
  interface LoginRequest {
    email: string
    password: string
  }

  interface RegisterRequest {
    username: string
    email: string
    password: string
    firstName: string
    lastName: string
  }

  interface AuthResponse {
    success: boolean
    message: string
    token?: string
    user?: User
  }

  // API Response types
  interface ApiResponse<T = unknown> {
    success: boolean
    message: string
    data?: T
    error?: string
  }

  interface PaginatedResponse<T> {
    success: boolean
    message: string
    data: T[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }

  // Permission check types
  interface PermissionCheck {
    module: string
    action: string
  }

  interface UserPermissions {
    permissions: Array<{
      module: string
      action: string
    }>
  }

  // Assignment types
  interface AssignUsersToGroupRequest {
    userIds: string[]
  }

  interface AssignRolesToGroupRequest {
    roleIds: string[]
  }

  interface AssignPermissionsToRoleRequest {
    permissionIds: string[]
  }

  // Form types
  interface FormErrors {
    [key: string]: string
  }

  interface LoadingState {
    [key: string]: boolean
  }

  // Navigation types
  interface NavItem {
    name: string
    path: string
    icon?: string
    requiresPermission?: {
      module: string
      action: string
    }
  }
}

export {}
