import { createContext } from 'react'

export interface AuthContextType {
  user: User | null
  permissions: UserPermissions | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: LoginRequest) => Promise<{ success: boolean; message: string }>
  register: (userData: RegisterRequest) => Promise<{ success: boolean; message: string }>
  logout: () => void
  refreshPermissions: () => Promise<void>
  hasPermission: (module: string, action: string) => boolean
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined)
